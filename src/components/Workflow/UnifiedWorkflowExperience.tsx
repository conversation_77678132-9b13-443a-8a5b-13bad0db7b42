/**
 * Unified Workflow Experience
 * 
 * Single-page workflow experience with integrated agent collaboration
 * and real-time backend integration
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { AgentCollaborationViewer } from './AgentCollaborationViewer';
import { RealTimeCollaborationMonitor } from './RealTimeCollaborationMonitor';
import { HumanInterventionPanel } from './HumanInterventionPanel';

// Types
interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  difficulty: string;
  estimatedTime: number;
  consultationEnabled: boolean;
  agentCount: number;
  steps: Array<{
    id: string;
    name: string;
    type: string;
    dependencies?: string[];
  }>;
}

interface WorkflowExecution {
  id: string;
  status: 'running' | 'paused' | 'completed' | 'failed' | 'waiting_review';
  progress: number;
  currentStep?: string;
  steps: Array<{
    id: string;
    name: string;
    status: string;
    outputs?: any;
    artifactId?: string;
  }>;
}

interface AgentActivity {
  agentId: string;
  status: 'idle' | 'analyzing' | 'responding' | 'waiting' | 'completed';
  lastSeen: string;
}

type WorkflowStep = 'template' | 'configure' | 'executing' | 'collaboration' | 'review' | 'results';

interface Props {
  onComplete?: (executionId: string) => void;
  onBack?: () => void;
}

export default function UnifiedWorkflowExperience({ onComplete, onBack }: Props) {
  // Core state
  const [currentStep, setCurrentStep] = useState<WorkflowStep>('template');
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [workflowInputs, setWorkflowInputs] = useState<Record<string, any>>({});
  const [execution, setExecution] = useState<WorkflowExecution | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Agent collaboration state
  const [agentActivities, setAgentActivities] = useState<AgentActivity[]>([]);
  const [isCollaborationActive, setIsCollaborationActive] = useState(false);
  const [collaborationResult, setCollaborationResult] = useState<any>(null);

  // UI state
  const [notifications, setNotifications] = useState<string[]>([]);
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);

  // Load templates on mount
  useEffect(() => {
    loadTemplates();
  }, []);

  // Auto-refresh execution status
  useEffect(() => {
    if (execution?.id && (currentStep === 'executing' || currentStep === 'collaboration')) {
      const interval = setInterval(() => {
        refreshExecutionStatus();
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [execution?.id, currentStep]);

  const addNotification = useCallback((message: string) => {
    setNotifications(prev => [...prev.slice(-2), message]);
    setTimeout(() => {
      setNotifications(prev => prev.slice(1));
    }, 5000);
  }, []);

  const loadTemplates = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/workflow/templates');
      const result = await response.json();
      
      if (result.success) {
        setTemplates(result.data.templates);
      } else {
        // Fallback to enhanced template selector data
        const fallbackTemplates: WorkflowTemplate[] = [
          {
            id: 'blog-post-seo',
            name: 'SEO Blog Post',
            description: 'Complete SEO-optimized blog post generation with keyword research and agent consultation',
            category: 'blog',
            tags: ['seo', 'blog', 'content-marketing'],
            difficulty: 'easy',
            estimatedTime: 45,
            consultationEnabled: true,
            agentCount: 3,
            steps: [
              { id: 'topic-input', name: 'Topic Input', type: 'TEXT_INPUT' },
              { id: 'keyword-research', name: 'Keyword Research', type: 'AI_GENERATION' },
              { id: 'content-creation', name: 'Content Creation', type: 'AI_GENERATION' },
              { id: 'human-review', name: 'Human Review', type: 'HUMAN_REVIEW' },
              { id: 'seo-optimization', name: 'SEO Optimization', type: 'AI_GENERATION' }
            ]
          },
          {
            id: 'product-descriptions',
            name: 'Bulk Product Descriptions',
            description: 'Generate product descriptions in bulk with brand voice consistency',
            category: 'ecommerce',
            tags: ['ecommerce', 'bulk', 'product-descriptions'],
            difficulty: 'medium',
            estimatedTime: 120,
            consultationEnabled: true,
            agentCount: 2,
            steps: [
              { id: 'csv-import', name: 'Import Product Data', type: 'CSV_IMPORT' },
              { id: 'brand-voice-input', name: 'Brand Voice Guidelines', type: 'TEXT_INPUT' },
              { id: 'bulk-generation', name: 'Generate Descriptions', type: 'LOOP' }
            ]
          }
        ];
        setTemplates(fallbackTemplates);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
      addNotification('Failed to load templates');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshExecutionStatus = async () => {
    if (!execution?.id) return;

    try {
      const response = await fetch(`/api/workflow/execution/${execution.id}`);
      const result = await response.json();
      
      if (result.success) {
        const updatedExecution = result.data;
        setExecution(updatedExecution);

        // Handle status changes
        if (updatedExecution.status === 'waiting_review' && currentStep !== 'review') {
          setCurrentStep('review');
          addNotification('Workflow paused for human review');
        } else if (updatedExecution.status === 'completed' && currentStep !== 'results') {
          setCurrentStep('results');
          addNotification('Workflow completed successfully!');
          if (onComplete) {
            onComplete(execution.id);
          }
        } else if (updatedExecution.status === 'failed') {
          setError('Workflow execution failed');
          addNotification('Workflow execution failed');
        }

        // Update agent activities based on execution status
        if (updatedExecution.status === 'running') {
          setAgentActivities([
            { agentId: 'seo-keyword', status: 'analyzing', lastSeen: new Date().toISOString() },
            { agentId: 'market-research', status: 'analyzing', lastSeen: new Date().toISOString() },
            { agentId: 'content-strategy', status: 'analyzing', lastSeen: new Date().toISOString() }
          ]);
        }
      }
    } catch (error) {
      console.error('Failed to refresh execution status:', error);
    }
  };

  const handleTemplateSelect = (template: WorkflowTemplate) => {
    setSelectedTemplate(template);
    
    // Initialize inputs based on template
    const initialInputs: Record<string, any> = {};
    if (template.id === 'blog-post-seo') {
      initialInputs.topic = '';
      initialInputs.target_audience = 'startups';
      initialInputs.primary_keyword = '';
    } else if (template.id === 'product-descriptions') {
      initialInputs.brand_voice = '';
      initialInputs.product_category = '';
    }
    
    setWorkflowInputs(initialInputs);
    setCurrentStep('configure');
    addNotification(`Selected template: ${template.name}`);
  };

  const handleInputChange = (key: string, value: string) => {
    setWorkflowInputs(prev => ({ ...prev, [key]: value }));
  };

  const startWorkflow = async () => {
    if (!selectedTemplate) return;

    try {
      setIsLoading(true);
      setCurrentStep('executing');
      addNotification('Starting workflow execution...');

      // Create and execute workflow
      const response = await fetch('/api/workflow/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: selectedTemplate.id,
          inputs: workflowInputs,
          userId: 'user-123' // TODO: Get from auth
        })
      });

      const result = await response.json();
      
      if (result.success) {
        const executionData: WorkflowExecution = {
          id: result.data.executionId,
          status: 'running',
          progress: 0,
          steps: selectedTemplate.steps.map(step => ({
            id: step.id,
            name: step.name,
            status: 'pending'
          }))
        };
        
        setExecution(executionData);
        addNotification(`Workflow started: ${result.data.executionId ? result.data.executionId.slice(-8) : 'Unknown'}`);

        // Auto-start agent collaboration for agent-enhanced templates
        if (selectedTemplate.consultationEnabled) {
          setTimeout(() => {
            setCurrentStep('collaboration');
            setIsCollaborationActive(true);
            addNotification('Agent collaboration started automatically');
          }, 2000);
        }
      } else {
        setError(result.error || 'Failed to start workflow');
        setCurrentStep('configure');
      }
    } catch (error) {
      console.error('Failed to start workflow:', error);
      setError('Failed to start workflow');
      setCurrentStep('configure');
    } finally {
      setIsLoading(false);
    }
  };

  const submitReview = async (decision: 'approved' | 'rejected', feedback?: string) => {
    if (!execution?.id) return;

    try {
      const response = await fetch('/api/workflow/execution/review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          executionId: execution.id,
          stepId: 'human-review',
          decision,
          feedback: feedback || '',
          reviewer: 'user-123' // TODO: Get from auth
        })
      });

      const result = await response.json();
      
      if (result.success) {
        addNotification(`Review ${decision}: Workflow resuming...`);
        setCurrentStep('executing');
      } else {
        setError(result.error || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Failed to submit review:', error);
      setError('Failed to submit review');
    }
  };

  const resetWorkflow = () => {
    setCurrentStep('template');
    setSelectedTemplate(null);
    setWorkflowInputs({});
    setExecution(null);
    setError(null);
    setIsCollaborationActive(false);
    setCollaborationResult(null);
    setAgentActivities([]);
    addNotification('Workflow reset');
  };

  return (
    <div
      className="min-h-screen bg-gray-50"
      style={{
        minHeight: '100vh',
        backgroundColor: '#f9fafb',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}
    >
      {/* Header with Progress */}
      <div
        className="bg-white shadow-sm border-b"
        style={{
          backgroundColor: 'white',
          borderBottom: '1px solid #e5e7eb',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              {onBack && (
                <button
                  onClick={onBack}
                  className="text-gray-600 hover:text-gray-900"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <div>
                <h1 className="text-xl font-semibold text-gray-900">AI Workflow Studio</h1>
                <p className="text-sm text-gray-600">Create content with intelligent agent collaboration</p>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="flex items-center space-x-2">
              {['Template', 'Configure', 'Execute', 'Collaborate', 'Review', 'Results'].map((stepName, index) => {
                const stepKeys: WorkflowStep[] = ['template', 'configure', 'executing', 'collaboration', 'review', 'results'];
                const currentStepIndex = stepKeys.indexOf(currentStep);
                const isActive = index === currentStepIndex;
                const isCompleted = index < currentStepIndex;
                const isAccessible = index <= currentStepIndex + 1;
                
                return (
                  <div key={stepName} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                      isActive ? 'bg-blue-600 text-white' :
                      isCompleted ? 'bg-green-600 text-white' :
                      isAccessible ? 'bg-gray-200 text-gray-600' :
                      'bg-gray-100 text-gray-400'
                    }`}>
                      {isCompleted ? '✓' : index + 1}
                    </div>
                    {index < 5 && (
                      <div className={`w-8 h-0.5 mx-1 ${
                        isCompleted ? 'bg-green-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="space-y-2">
            {notifications.map((notification, index) => (
              <div
                key={index}
                className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 animate-fade-in"
              >
                🔔 {notification}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-sm text-red-800">
            ❌ {error}
            <button
              onClick={() => setError(null)}
              className="ml-4 text-red-600 hover:text-red-800 underline"
            >
              Dismiss
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentStep === 'template' && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Workflow Template</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Select a template that matches your content goals. Agent-enhanced templates include intelligent collaboration for better results.
              </p>
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading templates...</span>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {templates.map((template) => (
                  <div
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className="bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all cursor-pointer group"
                  >
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-900">
                            {template.name}
                          </h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`px-2 py-1 text-xs font-medium rounded ${
                              template.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                              template.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {template.difficulty}
                            </span>
                            <span className="text-xs text-gray-500">~{template.estimatedTime}min</span>
                          </div>
                        </div>

                        {template.consultationEnabled && (
                          <div className="flex items-center space-x-1">
                            <span className="text-sm">🤖</span>
                            <span className="text-xs text-blue-600 font-medium">{template.agentCount}</span>
                          </div>
                        )}
                      </div>

                      <p className="text-gray-600 text-sm mb-4">{template.description}</p>

                      {template.consultationEnabled && (
                        <div className="mb-4">
                          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <span className="mr-1">🤖</span>
                            Agent-Enhanced
                          </div>
                        </div>
                      )}

                      <div className="space-y-2">
                        <div className="text-xs text-gray-600 font-medium">Workflow Steps:</div>
                        <div className="space-y-1">
                          {(template.steps || []).slice(0, 3).map((step, index) => (
                            <div key={step.id} className="flex items-center space-x-2 text-xs">
                              <span className="w-4 h-4 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-medium">
                                {index + 1}
                              </span>
                              <span className="text-gray-700">{step.name}</span>
                            </div>
                          ))}
                          {(template.steps || []).length > 3 && (
                            <div className="text-xs text-gray-500 ml-6">
                              +{(template.steps || []).length - 3} more steps
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="mt-4 pt-3 border-t border-gray-100">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-500">{template.category}</span>
                          <span className="text-blue-600 font-medium">Select Template →</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {currentStep === 'configure' && selectedTemplate && (
          <div className="max-w-2xl mx-auto space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedTemplate.name}</h2>
              <p className="text-gray-600">{selectedTemplate.description}</p>
              <button
                onClick={() => setCurrentStep('template')}
                className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                ← Change Template
              </button>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Configure Your Workflow</h3>

              <div className="space-y-4">
                {Object.entries(workflowInputs).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </label>
                    {key.includes('description') || key.includes('voice') ? (
                      <textarea
                        value={value}
                        onChange={(e) => handleInputChange(key, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows={3}
                        placeholder={`Enter ${key.replace(/_/g, ' ')}`}
                      />
                    ) : (
                      <input
                        type="text"
                        value={value}
                        onChange={(e) => handleInputChange(key, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={`Enter ${key.replace(/_/g, ' ')}`}
                      />
                    )}
                  </div>
                ))}
              </div>

              {selectedTemplate.consultationEnabled && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">🤖 Agent Collaboration Enabled</h4>
                  <p className="text-sm text-blue-800 mb-2">
                    This workflow includes intelligent agent consultation with {selectedTemplate.agentCount} specialized agents.
                  </p>
                  <div className="text-xs text-blue-700">
                    <strong>Agents:</strong> SEO Specialist, Market Research, Content Strategy
                  </div>
                </div>
              )}

              <div className="mt-6 flex justify-between">
                <button
                  onClick={() => setCurrentStep('template')}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Back
                </button>
                <button
                  onClick={startWorkflow}
                  disabled={isLoading || Object.values(workflowInputs).some(v => !v)}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Starting...' : 'Start Workflow'}
                </button>
              </div>
            </div>
          </div>
        )}

        {(currentStep === 'executing' || currentStep === 'collaboration') && execution && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {currentStep === 'executing' ? 'Workflow Executing' : 'Agent Collaboration Active'}
              </h2>
              <p className="text-gray-600">
                {currentStep === 'executing'
                  ? 'Your workflow is running and generating content...'
                  : 'Intelligent agents are collaborating to enhance your content...'
                }
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Execution Progress */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Workflow Progress</h3>

                <div className="mb-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Overall Progress</span>
                    <span>{execution.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${execution.progress}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-3">
                  {(execution.steps || []).map((step, index) => (
                    <div key={step.id} className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                        step.status === 'completed' ? 'bg-green-600 text-white' :
                        step.status === 'running' ? 'bg-blue-600 text-white animate-pulse' :
                        step.status === 'waiting_review' ? 'bg-yellow-600 text-white' :
                        step.status === 'failed' ? 'bg-red-600 text-white' :
                        'bg-gray-200 text-gray-600'
                      }`}>
                        {step.status === 'completed' ? '✓' :
                         step.status === 'running' ? '●' :
                         step.status === 'waiting_review' ? '⏸' :
                         step.status === 'failed' ? '✗' :
                         index + 1}
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">{step.name}</div>
                        <div className="text-xs text-gray-500 capitalize">{step.status.replace('_', ' ')}</div>
                      </div>
                      {step.artifactId && (
                        <div className="text-xs text-green-600">📄 Generated</div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-600">
                    <strong>Status:</strong> {execution.status.replace('_', ' ')}
                  </div>
                  {execution.currentStep && (
                    <div className="text-sm text-gray-600">
                      <strong>Current Step:</strong> {execution.currentStep}
                    </div>
                  )}
                </div>
              </div>

              {/* Agent Collaboration Monitor */}
              {selectedTemplate?.consultationEnabled && (
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Agent Collaboration</h3>

                  {isCollaborationActive ? (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2 text-sm text-blue-600">
                        <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                        <span>Agents are actively collaborating...</span>
                      </div>

                      <div className="space-y-3">
                        {agentActivities.map((activity) => (
                          <div key={activity.agentId} className="flex items-center space-x-3">
                            <div className={`w-3 h-3 rounded-full ${
                              activity.status === 'analyzing' ? 'bg-blue-500 animate-pulse' :
                              activity.status === 'responding' ? 'bg-green-500 animate-pulse' :
                              activity.status === 'completed' ? 'bg-green-600' :
                              'bg-gray-400'
                            }`}></div>
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900 capitalize">
                                {activity.agentId.replace('-', ' ')} Agent
                              </div>
                              <div className="text-xs text-gray-500 capitalize">
                                {activity.status}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <div className="text-gray-400 mb-2">
                        <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                      <div className="text-sm text-gray-600">
                        Agent collaboration will start automatically during content generation
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Live Activity Feed */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Activity Feed</h3>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="text-sm text-gray-500 text-center py-4">
                    Activity will appear here as the workflow progresses...
                  </div>
                ) : (
                  notifications.map((notification, index) => (
                    <div key={index} className="text-sm text-gray-700 p-2 bg-gray-50 rounded">
                      🔔 {notification}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}

        {currentStep === 'review' && execution && (
          <div className="max-w-2xl mx-auto space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Human Review Required</h2>
              <p className="text-gray-600">
                The workflow has paused for your review. Please examine the generated content and provide your decision.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Review Generated Content</h3>

              {/* Show generated content from execution steps */}
              <div className="space-y-4 mb-6">
                {(execution.steps || [])
                  .filter(step => step.outputs && step.outputs.content)
                  .map((step) => (
                    <div key={step.id} className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">{step.name}</h4>
                      <div className="text-sm text-gray-700 bg-gray-50 p-3 rounded max-h-32 overflow-y-auto">
                        {step.outputs.content}
                      </div>
                      {step.outputs.wordCount && (
                        <div className="text-xs text-gray-500 mt-2">
                          Word count: {step.outputs.wordCount}
                        </div>
                      )}
                    </div>
                  ))}
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={() => submitReview('approved')}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  ✅ Approve & Continue
                </button>
                <button
                  onClick={() => {
                    const feedback = prompt('Please provide feedback for improvements:');
                    if (feedback) {
                      submitReview('rejected', feedback);
                    }
                  }}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                  ❌ Reject & Provide Feedback
                </button>
              </div>
            </div>
          </div>
        )}

        {currentStep === 'results' && execution && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">🎉 Workflow Complete!</h2>
              <p className="text-gray-600">
                Your content has been generated successfully with agent collaboration and human review.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Generated Artifacts */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Generated Content</h3>
                <div className="space-y-4">
                  {(execution.steps || [])
                    .filter(step => step.artifactId)
                    .map((step) => (
                      <div key={step.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{step.name}</h4>
                          <span className="text-xs text-green-600">📄 Generated</span>
                        </div>
                        <div className="text-sm text-gray-700 bg-gray-50 p-3 rounded max-h-24 overflow-y-auto">
                          {step.outputs?.content?.substring(0, 200)}...
                        </div>
                      </div>
                    ))}
                </div>

                <button
                  onClick={() => {
                    window.open(`/workflow/agent-enhanced/artifacts/${execution.id}`, '_blank');
                  }}
                  className="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  📄 View All Artifacts
                </button>
              </div>

              {/* Workflow Summary */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Workflow Summary</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Template:</span>
                    <span className="text-sm font-medium text-gray-900">{selectedTemplate?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Steps Completed:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {(execution.steps || []).filter(s => s.status === 'completed').length} / {(execution.steps || []).length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Agent Collaboration:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {selectedTemplate?.consultationEnabled ? '✅ Enabled' : '❌ Disabled'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Execution ID:</span>
                    <span className="text-sm font-mono text-gray-900">{execution.id ? execution.id.slice(-8) : 'Unknown'}</span>
                  </div>
                </div>

                <div className="mt-6 space-y-3">
                  <button
                    onClick={resetWorkflow}
                    className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    🚀 Create Another Workflow
                  </button>
                  {onBack && (
                    <button
                      onClick={onBack}
                      className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                    >
                      ← Back to Dashboard
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
