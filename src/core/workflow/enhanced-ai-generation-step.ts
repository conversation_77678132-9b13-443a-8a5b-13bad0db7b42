/**
 * Enhanced AI Generation Step with Dynamic Agent Consultation
 * 
 * Integrates the fresh dynamic agent consultation system with workflow AI generation steps
 */

import { DynamicAgentConsultationService } from './dynamic-agent-consultation-service';
import { WorkflowAgentBridge } from './workflow-agent-bridge';
import { getAgentBridge, getConsultationService } from './singleton';
import {
  WorkflowStep,
  StepConfig,
  AgentConsultationConfig,
  StepType
} from './types';
import {
  AgentConsultationResponse,
  ConsultationContext
} from '../agents/types';

export class EnhancedAIGenerationStep {
  private consultationService: DynamicAgentConsultationService;
  private agentBridge: WorkflowAgentBridge;

  constructor() {
    // Use singleton services to ensure consistency across the workflow system
    this.agentBridge = getAgentBridge();
    this.consultationService = getConsultationService();

    console.log('🔧 Enhanced AI Generation Step initialized with singleton services');
  }

  /**
   * Execute AI generation step with optional agent consultation
   */
  async executeAIGenerationWithConsultation(
    step: WorkflowStep,
    inputs: Record<string, any>,
    executionId: string
  ): Promise<{
    outputs: Record<string, any>;
    consultationResults?: AgentConsultationResponse[];
    enhancedContent?: any;
  }> {
    console.log(`🚀 Executing enhanced AI generation step: ${step.id}`);

    try {
      // Extract consultation configuration
      const consultationConfig = step.consultationConfig;
      
      // Prepare consultation context
      const context = this.prepareConsultationContext(inputs, step);

      let consultationResults: AgentConsultationResponse[] = [];
      let enhancedContent: any = null;

      // Perform agent consultation if configured
      if (consultationConfig?.enabled) {
        console.log(`🤝 Performing agent consultation for step ${step.id}`);
        console.log(`🔧 Consultation config:`, {
          enabled: consultationConfig.enabled,
          triggers: consultationConfig.triggers.map(t => ({ type: t.type, agents: t.agents })),
          maxConsultations: consultationConfig.maxConsultations
        });
        console.log(`🔧 Consultation context:`, {
          topic: context.topic,
          contentType: context.contentType,
          targetAudience: context.targetAudience,
          qualityScore: context.qualityScore,
          complexity: context.complexity
        });

        consultationResults = await this.consultationService.consultMultipleAgents(
          executionId,
          step.id,
          context,
          consultationConfig
        );

        console.log(`✅ Completed ${consultationResults.length} agent consultations`);

        // Enhance content with consultation results
        if (consultationResults.length > 0) {
          enhancedContent = this.enhanceContentWithConsultations(
            inputs,
            consultationResults,
            step.config
          );
          console.log(`🔧 Content enhanced with ${consultationResults.length} consultation results`);
        } else {
          console.log(`⚠️ No consultation results to enhance content with`);
        }
      } else {
        console.log(`ℹ️ Agent consultation disabled for step ${step.id}`);
      }

      // Execute the original AI generation
      const aiOutputs = await this.executeOriginalAIGeneration(step, inputs, enhancedContent);

      // Combine results
      const outputs = {
        ...aiOutputs,
        ...(consultationResults.length > 0 && {
          consultationSummary: this.createConsultationSummary(consultationResults),
          agentInsights: this.extractAgentInsights(consultationResults)
        })
      };

      return {
        outputs,
        consultationResults,
        enhancedContent
      };

    } catch (error) {
      console.error(`❌ Enhanced AI generation failed for step ${step.id}:`, error);
      throw error;
    }
  }

  /**
   * Prepare consultation context from step inputs
   */
  private prepareConsultationContext(
    inputs: Record<string, any>,
    step: WorkflowStep
  ): ConsultationContext {
    // Calculate default quality score if not provided
    const defaultQualityScore = this.calculateDefaultQualityScore(inputs, step);

    // Calculate default complexity if not provided
    const defaultComplexity = this.calculateDefaultComplexity(inputs, step);

    return {
      topic: inputs.topic || inputs.subject || '',
      contentType: inputs.contentType || step.config.aiConfig?.contentType || 'blog-post',
      targetAudience: inputs.targetAudience || inputs.audience || 'general audience',
      primaryKeyword: inputs.primaryKeyword || inputs.keyword,
      industry: inputs.industry,
      goals: inputs.goals || ['inform', 'engage'],
      content: inputs.content || inputs.existingContent,
      feedback: inputs.feedback,
      qualityScore: inputs.qualityScore ?? defaultQualityScore,
      complexity: inputs.complexity ?? defaultComplexity,
      brandVoice: inputs.brandVoice || 'professional',
      geographicScope: inputs.geographicScope,
      competitorKeywords: inputs.competitorKeywords,
      contentPillars: inputs.contentPillars
    };
  }

  /**
   * Calculate a default quality score based on available inputs
   */
  private calculateDefaultQualityScore(inputs: Record<string, any>, step: WorkflowStep): number {
    let score = 0.5; // Base score

    // Increase score based on available inputs
    if (inputs.topic) score += 0.1;
    if (inputs.targetAudience) score += 0.1;
    if (inputs.primaryKeyword || inputs.keyword) score += 0.1;
    if (inputs.keyword_research) score += 0.1;
    if (inputs.industry) score += 0.05;
    if (inputs.brandVoice) score += 0.05;

    // For content creation steps, assume lower initial quality to trigger consultation
    if (step.id.includes('content') || step.name.toLowerCase().includes('content')) {
      score = Math.max(0.6, score); // Ensure it's below typical threshold of 0.7-0.8
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate default complexity based on inputs and step type
   */
  private calculateDefaultComplexity(inputs: Record<string, any>, step: WorkflowStep): number {
    let complexity = 0.3; // Base complexity

    // Increase complexity based on content characteristics
    if (inputs.topic) {
      const topic = inputs.topic.toLowerCase();
      const technicalTerms = ['api', 'algorithm', 'framework', 'architecture', 'technical', 'advanced'];
      if (technicalTerms.some(term => topic.includes(term))) {
        complexity += 0.3;
      }
    }

    if (inputs.targetAudience) {
      const audience = inputs.targetAudience.toLowerCase();
      if (audience.includes('expert') || audience.includes('professional') || audience.includes('technical')) {
        complexity += 0.2;
      }
    }

    // Content creation steps typically have higher complexity
    if (step.id.includes('content') || step.name.toLowerCase().includes('content')) {
      complexity += 0.2;
    }

    return Math.min(complexity, 1.0);
  }

  /**
   * Enhance content generation with consultation results
   */
  private enhanceContentWithConsultations(
    originalInputs: Record<string, any>,
    consultationResults: AgentConsultationResponse[],
    stepConfig: StepConfig
  ): any {
    const enhancements: any = {
      originalInputs,
      agentRecommendations: {},
      enhancedPrompt: stepConfig.aiConfig?.prompt || '',
      additionalContext: {}
    };

    consultationResults.forEach(result => {
      switch (result.agentId) {
        case 'seo-keyword':
          enhancements.agentRecommendations.seo = {
            keywords: result.response.keywordAnalysis?.primaryKeywords || [],
            recommendations: result.response.recommendations || [],
            suggestions: result.suggestions
          };
          enhancements.additionalContext.seoKeywords = result.response.keywordAnalysis?.primaryKeywords?.slice(0, 3).join(', ');
          break;

        case 'market-research':
          enhancements.agentRecommendations.market = {
            targetDemographics: result.response.marketAnalysis?.targetDemographics,
            marketTrends: result.response.marketAnalysis?.marketTrends || [],
            suggestions: result.suggestions
          };
          enhancements.additionalContext.marketInsights = result.response.insights?.slice(0, 2).join('. ');
          break;

        case 'content-strategy':
          enhancements.agentRecommendations.strategy = {
            contentOutline: result.response.strategyAnalysis?.contentOutline || [],
            toneAndVoice: result.response.strategyAnalysis?.toneAndVoice,
            suggestions: result.suggestions
          };
          enhancements.additionalContext.contentStructure = result.response.strategyAnalysis?.contentOutline?.map((section: any) => section.section).join(', ');
          break;
      }
    });

    // Enhance the AI prompt with agent insights
    enhancements.enhancedPrompt = this.enhancePromptWithAgentInsights(
      stepConfig.aiConfig?.prompt || '',
      enhancements.agentRecommendations,
      enhancements.additionalContext
    );

    return enhancements;
  }

  /**
   * Enhance AI prompt with agent insights
   */
  private enhancePromptWithAgentInsights(
    originalPrompt: string,
    agentRecommendations: any,
    additionalContext: any
  ): string {
    let enhancedPrompt = originalPrompt;

    // Add SEO context
    if (additionalContext.seoKeywords) {
      enhancedPrompt += `\n\nSEO GUIDANCE: Focus on these keywords: ${additionalContext.seoKeywords}`;
    }

    // Add market research context
    if (additionalContext.marketInsights) {
      enhancedPrompt += `\n\nMARKET INSIGHTS: ${additionalContext.marketInsights}`;
    }

    // Add content strategy context
    if (additionalContext.contentStructure) {
      enhancedPrompt += `\n\nCONTENT STRUCTURE: Consider these sections: ${additionalContext.contentStructure}`;
    }

    // Add specific recommendations
    const allSuggestions = Object.values(agentRecommendations)
      .flatMap((rec: any) => rec.suggestions || [])
      .filter((suggestion: any) => suggestion.priority === 'high')
      .slice(0, 3);

    if (allSuggestions.length > 0) {
      enhancedPrompt += `\n\nKEY RECOMMENDATIONS:\n${allSuggestions.map((s: any) => `- ${s.suggestion}`).join('\n')}`;
    }

    return enhancedPrompt;
  }

  /**
   * Execute original AI generation (placeholder for actual AI integration)
   */
  private async executeOriginalAIGeneration(
    step: WorkflowStep,
    inputs: Record<string, any>,
    enhancedContent?: any
  ): Promise<Record<string, any>> {
    // This would integrate with the actual AI generation logic
    // For now, return a simulated response
    
    const prompt = enhancedContent?.enhancedPrompt || step.config.aiConfig?.prompt || '';
    const topic = inputs.topic || 'the given topic';

    // Simulate AI generation with enhanced context
    const generatedContent = {
      title: `${topic} - Comprehensive Guide`,
      content: `This is AI-generated content for "${topic}" enhanced with agent consultations.`,
      metadata: {
        wordCount: 1500,
        readingTime: '6 minutes',
        seoScore: enhancedContent?.agentRecommendations?.seo ? 85 : 70,
        enhancedWithAgents: !!enhancedContent
      }
    };

    // Add agent-specific enhancements to the output
    if (enhancedContent?.agentRecommendations) {
      const agentEnhancements = {
        seoOptimized: !!enhancedContent.agentRecommendations.seo,
        marketResearched: !!enhancedContent.agentRecommendations.market,
        strategicallyPlanned: !!enhancedContent.agentRecommendations.strategy
      };

      generatedContent.metadata = {
        ...generatedContent.metadata,
        ...agentEnhancements, // Spread the enhancements directly into metadata
        agentEnhancements
      };
    }

    return {
      [step.outputs[0] || 'content']: generatedContent
    };
  }

  /**
   * Create consultation summary
   */
  private createConsultationSummary(consultationResults: AgentConsultationResponse[]): any {
    return {
      totalConsultations: consultationResults.length,
      averageConfidence: consultationResults.reduce((sum, result) => sum + result.confidence, 0) / consultationResults.length,
      consultedAgents: consultationResults.map(result => result.agentId),
      totalProcessingTime: consultationResults.reduce((sum, result) => sum + result.processingTime, 0),
      keyInsights: consultationResults.flatMap(result => 
        result.suggestions.filter(s => s.priority === 'high').slice(0, 2)
      )
    };
  }

  /**
   * Extract agent insights for workflow context
   */
  private extractAgentInsights(consultationResults: AgentConsultationResponse[]): Record<string, any> {
    const insights: Record<string, any> = {};

    consultationResults.forEach(result => {
      insights[result.agentId] = {
        confidence: result.confidence,
        keyRecommendations: result.suggestions.filter(s => s.priority === 'high').map(s => s.suggestion),
        processingTime: result.processingTime,
        reasoning: result.reasoning
      };
    });

    return insights;
  }

  /**
   * Get consultation service metrics
   */
  getConsultationMetrics() {
    return this.consultationService.getMetrics();
  }

  /**
   * Get agent bridge status
   */
  async getAgentStatus() {
    return await this.agentBridge.getAllAgentStatus();
  }

  /**
   * Perform health check on the agent system
   */
  async performHealthCheck() {
    return await this.agentBridge.performHealthCheck();
  }
}
