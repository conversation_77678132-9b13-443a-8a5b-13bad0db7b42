/**
 * Singleton instances for workflow system
 * Ensures all API routes use the same instances
 */

import { WorkflowEngine } from './engine';
import { SimplifiedStateStore } from '../state/store';
import { RedisStorageAdapter } from '../state/redis-adapter';
import { MemoryStorageAdapter } from '../state/types';
import { AIModelManager } from '../ai/model-manager';
import { SimplifiedReviewSystem } from '../review/system';
import { TemplateRegistry } from './templates';
import { EnhancedTemplateRegistry } from './template-processor';
import { environmentValidator, enforceProductionRequirements } from '../utils/environment-validator';
import { errorHandler, ErrorType, ErrorSeverity } from '../utils/error-handler';
import { WorkflowAgentBridge } from './workflow-agent-bridge';
import { DynamicAgentConsultationService } from './dynamic-agent-consultation-service';
import { SeoKeywordAgent } from '../agents/seo-keyword-agent';
import { MarketResearchAgent } from '../agents/market-research-agent';
import { ContentStrategyAgent } from '../agents/content-strategy-agent';

// Create singleton instances
let stateStoreInstance: SimplifiedStateStore | null = null;
let aiManagerInstance: AIModelManager | null = null;
let reviewSystemInstance: SimplifiedReviewSystem | null = null;
let workflowEngineInstance: WorkflowEngine | null = null;
let templateRegistryInstance: TemplateRegistry | null = null;
let enhancedTemplateRegistryInstance: EnhancedTemplateRegistry | null = null;
let agentBridgeInstance: WorkflowAgentBridge | null = null;
let consultationServiceInstance: DynamicAgentConsultationService | null = null;

export function getStateStore(): SimplifiedStateStore {
  if (!stateStoreInstance) {
    try {
      // Validate environment and enforce Redis in production
      const validation = environmentValidator.validateEnvironment();

      if (process.env.NODE_ENV === 'production') {
        enforceProductionRequirements();

        if (!validation.config.redisUrl || !validation.config.redisToken) {
          const error = errorHandler.createError(
            ErrorType.SYSTEM,
            'REDIS_REQUIRED_PRODUCTION',
            'Redis configuration is required in production environment',
            { nodeEnv: process.env.NODE_ENV },
            ErrorSeverity.CRITICAL
          );
          throw error;
        }

        // Use Redis adapter for production
        const redisAdapter = new RedisStorageAdapter('workflow:', 60 * 60 * 24 * 7); // 7 days TTL
        stateStoreInstance = new SimplifiedStateStore(redisAdapter);
        console.log('🔴 Production: Using Redis storage adapter');
      } else {
        // Development: Use Redis if available, otherwise memory
        if (validation.config.redisUrl && validation.config.redisToken) {
          const redisAdapter = new RedisStorageAdapter('workflow:', 60 * 60 * 24 * 7);
          stateStoreInstance = new SimplifiedStateStore(redisAdapter);
          console.log('🟡 Development: Using Redis storage adapter');
        } else {
          const memoryAdapter = new MemoryStorageAdapter();
          stateStoreInstance = new SimplifiedStateStore(memoryAdapter);
          console.log('🟡 Development: Using memory storage adapter (data will not persist)');
        }
      }

      stateStoreInstance.initialize().catch(error => {
        const standardError = errorHandler.handleError(error, {
          operation: 'initializeStateStore',
          nodeEnv: process.env.NODE_ENV
        });
        console.error('❌ Failed to initialize state store:', standardError.message);
      });

    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'getStateStore',
        nodeEnv: process.env.NODE_ENV
      });
      console.error('🚨 Critical error initializing state store:', standardError.message);
      throw standardError;
    }
  }
  return stateStoreInstance;
}

export function getAIManager(): AIModelManager {
  if (!aiManagerInstance) {
    aiManagerInstance = new AIModelManager({
      defaultProvider: 'openai',
      defaultModel: 'gpt-4',
      providers: {
        openai: {
          apiKey: process.env.OPENAI_API_KEY || ''
        }
      },
      costTracking: true,
      rateLimiting: {
        enabled: false,
        requestsPerMinute: 60,
        tokensPerMinute: 100000
      }
    });
  }
  return aiManagerInstance;
}

export function getReviewSystem(): SimplifiedReviewSystem {
  if (!reviewSystemInstance) {
    reviewSystemInstance = new SimplifiedReviewSystem(getStateStore());
  }
  return reviewSystemInstance;
}

export function getWorkflowEngine(): WorkflowEngine {
  if (!workflowEngineInstance) {
    workflowEngineInstance = new WorkflowEngine(
      getStateStore(),
      getAIManager(),
      getReviewSystem()
    );
  }
  return workflowEngineInstance;
}

export function getTemplateRegistry(): TemplateRegistry {
  if (!templateRegistryInstance) {
    templateRegistryInstance = new TemplateRegistry();
  }
  return templateRegistryInstance;
}

export function getEnhancedTemplateRegistry(): EnhancedTemplateRegistry {
  if (!enhancedTemplateRegistryInstance) {
    enhancedTemplateRegistryInstance = new EnhancedTemplateRegistry();

    // Register essential templates
    const { ESSENTIAL_TEMPLATES } = require('./templates');
    ESSENTIAL_TEMPLATES.forEach((template: any) => {
      try {
        enhancedTemplateRegistryInstance!.registerTemplate(template);
      } catch (error) {
        console.error(`Failed to register template ${template.id}:`, error);
      }
    });

    console.log(`📋 Enhanced template registry initialized with ${ESSENTIAL_TEMPLATES.length} templates`);
  }
  return enhancedTemplateRegistryInstance;
}

export function getAgentBridge(): WorkflowAgentBridge {
  if (!agentBridgeInstance) {
    // Initialize fresh agent system
    const seoAgent = new SeoKeywordAgent();
    const marketAgent = new MarketResearchAgent();
    const strategyAgent = new ContentStrategyAgent();

    // Create agent bridge with all agents
    agentBridgeInstance = new WorkflowAgentBridge({
      'seo-keyword': seoAgent,
      'market-research': marketAgent,
      'content-strategy': strategyAgent
    });

    console.log('🤖 Singleton agent bridge initialized with 3 agents');
  }
  return agentBridgeInstance;
}

export function getConsultationService(): DynamicAgentConsultationService {
  if (!consultationServiceInstance) {
    // Use the singleton agent bridge
    consultationServiceInstance = new DynamicAgentConsultationService(getAgentBridge());
    console.log('🤝 Singleton consultation service initialized');
  }
  return consultationServiceInstance;
}
